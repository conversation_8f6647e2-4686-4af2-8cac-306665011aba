import React from 'react';
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { BookO<PERSON>, Users, Clock, Edit, Trash2 } from 'lucide-react';
import { type Course } from '../types';
import { useAuth } from '../hooks/useAuth';

interface CourseCardProps {
  course: Course;
  onEdit?: (course: Course) => void;
  onDelete?: (courseId: number) => void;
  onView?: (courseId: number) => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ 
  course, 
  onEdit, 
  onDelete, 
  onView 
}) => {
  const { state } = useAuth();
  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';
  const isOwner = state.user?.id === course.creator_id;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleCardClick = () => {
    if (onView) {
      onView(course.id);
    }
  };

  return (
    <Card className="h-100 course-card" style={{ cursor: onView ? 'pointer' : 'default' }}>
      {course.thumbnail && (
        <Card.Img 
          variant="top" 
          src={course.thumbnail} 
          style={{ height: '200px', objectFit: 'cover' }}
          onClick={handleCardClick}
        />
      )}
      
      <Card.Body className="d-flex flex-column">
        <div className="d-flex justify-content-between align-items-start mb-2">
          <Card.Title 
            className="h5 mb-0" 
            onClick={handleCardClick}
            style={{ cursor: onView ? 'pointer' : 'default' }}
          >
            {course.title}
          </Card.Title>
          <div className="d-flex gap-1">
            <Badge bg={course.is_published ? 'success' : 'warning'}>
              {course.is_published ? 'Published' : 'Draft'}
            </Badge>
          </div>
        </div>

        <Card.Text className="text-muted small mb-2">
          {course.description || 'No description available'}
        </Card.Text>

        <div className="d-flex align-items-center text-muted small mb-2">
          <BookOpen size={14} className="me-1" />
          <span className="me-3">
            {course.lessons?.length || 0} lessons
          </span>
          <Clock size={14} className="me-1" />
          <span>Created {formatDate(course.created_at)}</span>
        </div>

        {course.creator && (
          <div className="text-muted small mb-3">
            <strong>Instructor:</strong> {course.creator.first_name} {course.creator.last_name}
          </div>
        )}

        <div className="mt-auto">
          <div className="d-flex gap-2">
            {onView && (
              <Button 
                variant="primary" 
                size="sm" 
                onClick={handleCardClick}
                className="flex-grow-1"
              >
                <BookOpen size={14} className="me-1" />
                View Course
              </Button>
            )}
            
            {isTeacherOrAdmin && (isOwner || state.user?.role === 'admin') && (
              <>
                {onEdit && (
                  <Button 
                    variant="outline-secondary" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(course);
                    }}
                  >
                    <Edit size={14} />
                  </Button>
                )}
                
                {onDelete && (
                  <Button 
                    variant="outline-danger" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(course.id);
                    }}
                  >
                    <Trash2 size={14} />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default CourseCard;
