{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "bootstrap": "^5.3.6", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "remark-gfm": "^4.0.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}